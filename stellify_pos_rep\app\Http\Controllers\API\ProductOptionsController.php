<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\BoutiqueOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductOptionsController extends Controller
{
    /**
     * Display a listing of product options with search and pagination
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $perPage = $request->get('per_page', 10);

        $options = BoutiqueOption::search($search)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // Keep search parameters in pagination links
        $options->appends($request->query());

        return view('product_options.index', compact('options', 'search'));
    }

    /**
     * Show the form for creating a new product option
     */
    public function create()
    {
        return view('product_options.create');
    }

    /**
     * Store a newly created product option
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:boutique_options,name',
            'price' => 'required|numeric|min:0|max:999999.99'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $option = BoutiqueOption::create([
                'name' => $request->name,
                'price' => $request->price
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product option created successfully!',
                'option' => $option
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product option: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing a product option
     */
    public function edit($id)
    {
        $option = BoutiqueOption::findOrFail($id);
        return response()->json([
            'success' => true,
            'option' => $option
        ]);
    }

    /**
     * Update the specified product option
     */
    public function update(Request $request, $id)
    {
        $option = BoutiqueOption::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:boutique_options,name,' . $id,
            'price' => 'required|numeric|min:0|max:999999.99'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $option->update([
                'name' => $request->name,
                'price' => $request->price
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product option updated successfully!',
                'option' => $option
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product option: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified product option
     */
    public function destroy($id)
    {
        try {
            $option = BoutiqueOption::findOrFail($id);
            
            // Check if option is being used in any orders (optional)
            // You can add this check if needed
            
            $option->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product option deleted successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product option: ' . $e->getMessage()
            ], 500);
        }
    }
}
