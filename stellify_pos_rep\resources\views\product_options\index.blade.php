@extends('layouts.app')

@section('title', 'Product Options')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs"></i> Product Options Management</h2>
                <button class="btn btn-primary" data-toggle="modal" data-target="#optionModal" id="addNewOptionBtn">
                    <i class="fas fa-plus"></i> Add New Option
                </button>
            </div>
            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('product-options.index') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" value="{{ $search }}" placeholder="Search by name...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <select class="form-control d-inline-block w-auto mr-2" name="per_page" onchange="this.form.submit()" style="width: auto !important;">
                                    <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 per page</option>
                                    <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                                    <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                                </select>
                                @if($search)
                                    <a href="{{ route('product-options.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                @endif
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Options Table -->
            <div class="card">
                <div class="card-body">
                    @if($options->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($options as $option)
                                        <tr>
                                            <td>{{ $option->id }}</td>
                                            <td>{{ $option->name }}</td>
                                            <td class="price-display">₹{{ number_format($option->price, 2) }}</td>
                                            <td>{{ $option->created_at ? $option->created_at->format('d-m-Y') : 'N/A' }}</td>
                                            <td class="table-actions">
                                                <button class="btn btn-sm btn-outline-primary edit-option-btn" data-id="{{ $option->id }}" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info">
                                Showing {{ $options->firstItem() }}-{{ $options->lastItem() }} of {{ $options->total() }} items
                            </div>
                            <div>
                                {{ $options->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No product options found</h5>
                            @if($search)
                                <p class="text-muted">No options match your search criteria.</p>
                                <a href="{{ route('product-options.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left"></i> View All Options
                                </a>
                            @else
                                <p class="text-muted">Get started by creating your first product option.</p>
                                <button class="btn btn-primary" data-toggle="modal" data-target="#optionModal" id="addFirstOptionBtn">
                                    <i class="fas fa-plus"></i> Add First Option
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Option Modal -->
<div class="modal fade" id="optionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Add New Option</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="optionForm">
                    @csrf
                    <div class="form-group">
                        <label for="optionName">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="optionName" required maxlength="255">
                        <div class="invalid-feedback" id="nameError" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="optionPrice">Price (₹) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="optionPrice" step="0.01" min="0" max="999999.99" required>
                        <div class="invalid-feedback" id="priceError" style="display: none;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveOption()" id="saveButton">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .price-display {
        font-weight: bold;
        color: #28a745;
    }
    .table-actions {
        white-space: nowrap;
    }
    .pagination-info {
        color: #6c757d;
        font-size: 0.9em;
    }
</style>
@endpush

@push('scripts')
<script>
    let editingOptionId = null;

    // Document ready
    $(document).ready(function() {
        console.log('DOM loaded, initializing product options...');

        // Check if jQuery and Bootstrap are loaded
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded!');
            return;
        }
        console.log('jQuery is loaded');

        // Check if modal element exists
        const modalElement = $('#optionModal');
        if (modalElement.length === 0) {
            console.error('Modal element not found!');
            return;
        }
        console.log('Modal element found');

        // Add New Option button click
        $('#addNewOptionBtn').on('click', function(e) {
            e.preventDefault();
            console.log('Add new option button clicked');
            openCreateModal();
        });

        // Add First Option button click (if exists)
        $('#addFirstOptionBtn').on('click', function(e) {
            e.preventDefault();
            console.log('Add first option button clicked');
            openCreateModal();
        });

        // Edit option buttons
        $('.edit-option-btn').on('click', function(e) {
            e.preventDefault();
            const optionId = $(this).data('id');
            console.log('Edit button clicked for option ID:', optionId);
            editOption(optionId);
        });

        console.log('Event listeners attached successfully');
    });

    // Open create modal
    function openCreateModal() {
        console.log('Opening create modal...');
        editingOptionId = null;
        $('#modalTitle').text('Add New Option');
        $('#optionForm')[0].reset();
        clearValidationErrors();

        // Show the modal using Bootstrap 4 syntax
        $('#optionModal').modal('show');
        console.log('Modal shown');
    }

    // Edit option
    async function editOption(id) {
        try {
            console.log('Editing option with ID:', id);
            const response = await fetch(`/product-options/${id}/edit`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Edit response data:', data);

            if (data.success) {
                editingOptionId = id;
                $('#modalTitle').text('Edit Option');
                $('#optionName').val(data.option.name);
                $('#optionPrice').val(data.option.price);
                clearValidationErrors();

                // Show the modal using Bootstrap 4 syntax
                $('#optionModal').modal('show');
                console.log('Edit modal shown');
            } else {
                showError('Failed to load option: ' + data.message);
            }
        } catch (error) {
            console.error('Error in editOption:', error);
            showError('Error loading option: ' + error.message);
        }
    }

    // Save option (create or update)
    async function saveOption() {
        try {
            const name = $('#optionName').val().trim();
            const price = parseFloat($('#optionPrice').val());

            if (!name) {
                showValidationError('nameError', 'Name is required');
                return;
            }

            if (isNaN(price) || price < 0) {
                showValidationError('priceError', 'Valid price is required');
                return;
            }

            const $saveButton = $('#saveButton');
            $saveButton.prop('disabled', true);
            $saveButton.html('<i class="fas fa-spinner fa-spin"></i> Saving...');

            const url = editingOptionId ? `/product-options/${editingOptionId}` : '/product-options';
            const method = editingOptionId ? 'PUT' : 'POST';

            const formData = new FormData();
            formData.append('name', name);
            formData.append('price', price);
            formData.append('_token', $('input[name="_token"]').val());

            if (editingOptionId) {
                formData.append('_method', 'PUT');
            }

            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                console.log('Option saved successfully');
                // Hide the modal using Bootstrap 4 syntax
                $('#optionModal').modal('hide');
                location.reload(); // Reload to show updated data
            } else {
                if (data.errors) {
                    if (data.errors.name) {
                        showValidationError('nameError', data.errors.name[0]);
                    }
                    if (data.errors.price) {
                        showValidationError('priceError', data.errors.price[0]);
                    }
                } else {
                    showError(data.message);
                }
            }
        } catch (error) {
            showError('Error saving option: ' + error.message);
        } finally {
            const $saveButton = $('#saveButton');
            $saveButton.prop('disabled', false);
            $saveButton.html('<i class="fas fa-save"></i> Save');
        }
    }



    // Utility functions
    function showError(message) {
        alert('Error: ' + message);
    }

    function showValidationError(fieldId, message) {
        const $field = $('#' + fieldId);
        $field.text(message);
        $field.show();

        const $input = $field.prev();
        $input.addClass('is-invalid');
    }

    function clearValidationErrors() {
        $('.invalid-feedback').hide().text('');
        $('.is-invalid').removeClass('is-invalid');
    }
</script>
@endpush
