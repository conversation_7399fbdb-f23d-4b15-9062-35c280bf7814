@extends('layouts.app')

@section('title', 'Product Options')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs"></i> Product Options Management</h2>
                <button class="btn btn-primary" data-toggle="modal" data-target="#optionModal" id="addNewOptionBtn" onclick="openCreateModal()">
                    <i class="fas fa-plus"></i> Add New Option
                </button>
            </div>
            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('product-options.index') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" value="{{ $search }}" placeholder="Search by name...">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <select class="form-control d-inline-block w-auto mr-2" name="per_page" onchange="this.form.submit()" style="width: auto !important;">
                                    <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 per page</option>
                                    <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                                    <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                                </select>
                                @if($search)
                                    <a href="{{ route('product-options.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                @endif
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Options Table -->
            <div class="card">
                <div class="card-body">
                    @if($options->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($options as $option)
                                        <tr>
                                            <td>{{ $option->id }}</td>
                                            <td>{{ $option->name }}</td>
                                            <td class="price-display">₹{{ number_format($option->price, 2) }}</td>
                                            <td>{{ $option->created_at ? $option->created_at->format('d-m-Y') : 'N/A' }}</td>
                                            <td class="table-actions">
                                                <button class="btn btn-sm btn-outline-primary edit-option-btn" data-id="{{ $option->id }}" title="Edit" onclick="editOption({{ $option->id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info">
                                Showing {{ $options->firstItem() }}-{{ $options->lastItem() }} of {{ $options->total() }} items
                            </div>
                            <div>
                                {{ $options->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No product options found</h5>
                            @if($search)
                                <p class="text-muted">No options match your search criteria.</p>
                                <a href="{{ route('product-options.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left"></i> View All Options
                                </a>
                            @else
                                <p class="text-muted">Get started by creating your first product option.</p>
                                <button class="btn btn-primary" data-toggle="modal" data-target="#optionModal" id="addFirstOptionBtn" onclick="openCreateModal()">
                                    <i class="fas fa-plus"></i> Add First Option
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Option Modal -->
<div class="modal fade" id="optionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Add New Option</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="optionForm">
                    @csrf
                    <div class="form-group">
                        <label for="optionName">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="optionName" required maxlength="255">
                        <div class="invalid-feedback" id="nameError" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="optionPrice">Price (₹) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="optionPrice" step="0.01" min="0" max="999999.99" required>
                        <div class="invalid-feedback" id="priceError" style="display: none;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveOption()" id="saveButton">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
        </div>
    </div>
</div>
@endsection



@push('styles')
<!-- Ensure Bootstrap 4 CSS is loaded -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    .price-display {
        font-weight: bold;
        color: #28a745;
    }
    .table-actions {
        white-space: nowrap;
    }
    .pagination-info {
        color: #6c757d;
        font-size: 0.9em;
    }

    /* Modal fallback styles */
    .modal.show {
        display: block !important;
    }
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1040;
        width: 100vw;
        height: 100vh;
        background-color: #000;
        opacity: 0.5;
    }
    .modal-open {
        overflow: hidden;
    }
</style>
@endpush

@push('scripts')
<!-- Ensure jQuery is loaded -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap 4 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
    let editingOptionId = null;

    // Global functions for onclick handlers
    // Open create modal
    function openCreateModal() {
        console.log('Opening create modal...');
        editingOptionId = null;

        // Reset form and clear errors
        try {
            document.getElementById('modalTitle').textContent = 'Add New Option';
            document.getElementById('optionForm').reset();
            clearValidationErrors();
        } catch (e) {
            console.error('Error resetting form:', e);
        }

        // Show the modal using multiple methods for compatibility
        try {
            if (typeof $ !== 'undefined' && $.fn.modal) {
                $('#optionModal').modal('show');
                console.log('Modal shown using jQuery');
            } else {
                // Fallback: manually show modal
                const modal = document.getElementById('optionModal');
                if (modal) {
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');
                    console.log('Modal shown using fallback method');
                }
            }
        } catch (e) {
            console.error('Error showing modal:', e);
        }
    }

    // Edit option
    async function editOption(id) {
        try {
            console.log('Editing option with ID:', id);
            const response = await fetch(`/product-options/${id}/edit`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Edit response data:', data);

            if (data.success) {
                editingOptionId = id;

                // Set form values
                try {
                    document.getElementById('modalTitle').textContent = 'Edit Option';
                    document.getElementById('optionName').value = data.option.name;
                    document.getElementById('optionPrice').value = data.option.price;
                    clearValidationErrors();
                } catch (e) {
                    console.error('Error setting form values:', e);
                }

                // Show the modal using multiple methods for compatibility
                try {
                    if (typeof $ !== 'undefined' && $.fn.modal) {
                        $('#optionModal').modal('show');
                        console.log('Edit modal shown using jQuery');
                    } else {
                        // Fallback: manually show modal
                        const modal = document.getElementById('optionModal');
                        if (modal) {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                            document.body.classList.add('modal-open');
                            console.log('Edit modal shown using fallback method');
                        }
                    }
                } catch (e) {
                    console.error('Error showing edit modal:', e);
                }
            } else {
                showError('Failed to load option: ' + data.message);
            }
        } catch (error) {
            console.error('Error in editOption:', error);
            showError('Error loading option: ' + error.message);
        }
    }





    // Save option (create or update)
    async function saveOption() {
        try {
            const name = $('#optionName').val().trim();
            const price = parseFloat($('#optionPrice').val());

            if (!name) {
                showValidationError('nameError', 'Name is required');
                return;
            }

            if (isNaN(price) || price < 0) {
                showValidationError('priceError', 'Valid price is required');
                return;
            }

            const $saveButton = $('#saveButton');
            $saveButton.prop('disabled', true);
            $saveButton.html('<i class="fas fa-spinner fa-spin"></i> Saving...');

            const url = editingOptionId ? `/product-options/${editingOptionId}` : '/product-options';
            const method = editingOptionId ? 'PUT' : 'POST';

            const formData = new FormData();
            formData.append('name', name);
            formData.append('price', price);
            formData.append('_token', $('input[name="_token"]').val());

            if (editingOptionId) {
                formData.append('_method', 'PUT');
            }

            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                console.log('Option saved successfully');
                // Hide the modal
                closeModal();
                location.reload(); // Reload to show updated data
            } else {
                if (data.errors) {
                    if (data.errors.name) {
                        showValidationError('nameError', data.errors.name[0]);
                    }
                    if (data.errors.price) {
                        showValidationError('priceError', data.errors.price[0]);
                    }
                } else {
                    showError(data.message);
                }
            }
        } catch (error) {
            showError('Error saving option: ' + error.message);
        } finally {
            const $saveButton = $('#saveButton');
            $saveButton.prop('disabled', false);
            $saveButton.html('<i class="fas fa-save"></i> Save');
        }
    }



    // Close modal function
    function closeModal() {
        try {
            if (typeof $ !== 'undefined' && $.fn.modal) {
                $('#optionModal').modal('hide');
                console.log('Modal hidden using jQuery');
            } else {
                // Fallback: manually hide modal
                const modal = document.getElementById('optionModal');
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');
                    console.log('Modal hidden using fallback method');
                }
            }
        } catch (e) {
            console.error('Error hiding modal:', e);
        }
    }

    // Utility functions
    function showError(message) {
        alert('Error: ' + message);
    }

    function showValidationError(fieldId, message) {
        try {
            const field = document.getElementById(fieldId);
            if (field) {
                field.textContent = message;
                field.style.display = 'block';

                const input = field.previousElementSibling;
                if (input) {
                    input.classList.add('is-invalid');
                }
            }
        } catch (e) {
            console.error('Error showing validation error:', e);
        }
    }

    function clearValidationErrors() {
        try {
            const feedbacks = document.querySelectorAll('.invalid-feedback');
            feedbacks.forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });

            const invalids = document.querySelectorAll('.is-invalid');
            invalids.forEach(el => {
                el.classList.remove('is-invalid');
            });
        } catch (e) {
            console.error('Error clearing validation errors:', e);
        }
    }

    // Document ready for additional event listeners (backup)
    $(document).ready(function() {
        console.log('DOM loaded, setting up backup event listeners...');

        // Backup event listeners (in case onclick doesn't work)
        $('#addNewOptionBtn').on('click', function(e) {
            console.log('jQuery: Add new option button clicked');
            openCreateModal();
        });

        $('#addFirstOptionBtn').on('click', function(e) {
            console.log('jQuery: Add first option button clicked');
            openCreateModal();
        });

        $('.edit-option-btn').on('click', function(e) {
            const optionId = $(this).data('id');
            console.log('jQuery: Edit button clicked for option ID:', optionId);
            editOption(optionId);
        });

        console.log('Backup event listeners attached successfully');
    });
</script>
@endpush
